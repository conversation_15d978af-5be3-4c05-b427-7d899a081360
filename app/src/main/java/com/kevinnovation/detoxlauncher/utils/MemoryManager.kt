package com.kevinnovation.detoxlauncher.utils

import android.app.ActivityManager
import android.content.Context
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * Memory management utilities to prevent memory leaks and optimize memory usage
 */
object MemoryManager {
    
    private const val TAG = "MemoryManager"
    private val activeScopes = mutableSetOf<WeakReference<CoroutineScope>>()
    
    /**
     * Check if device is running low on memory
     */
    fun isLowMemory(context: Context): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val isLowMemory = memoryInfo.lowMemory
        if (isLowMemory) {
            Log.w(TAG, "Device is running low on memory")
            Log.w(TAG, "Available memory: ${memoryInfo.availMem / 1024 / 1024}MB")
            Log.w(TAG, "Threshold: ${memoryInfo.threshold / 1024 / 1024}MB")
        }
        
        return isLowMemory
    }
    
    /**
     * Get memory usage information
     */
    fun getMemoryInfo(context: Context): MemoryInfo {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val runtime = Runtime.getRuntime()
        
        return MemoryInfo(
            totalDeviceMemory = memoryInfo.totalMem,
            availableDeviceMemory = memoryInfo.availMem,
            isLowMemory = memoryInfo.lowMemory,
            appMaxMemory = runtime.maxMemory(),
            appUsedMemory = runtime.totalMemory() - runtime.freeMemory(),
            appFreeMemory = runtime.freeMemory()
        )
    }
    
    /**
     * Register a coroutine scope for cleanup
     */
    fun registerScope(scope: CoroutineScope) {
        activeScopes.add(WeakReference(scope))
        cleanupDeadReferences()
    }
    
    /**
     * Cancel all registered scopes
     */
    fun cancelAllScopes() {
        Log.d(TAG, "Cancelling all registered coroutine scopes")
        activeScopes.forEach { weakRef ->
            weakRef.get()?.cancel("Memory cleanup")
        }
        activeScopes.clear()
    }
    
    /**
     * Clean up dead weak references
     */
    private fun cleanupDeadReferences() {
        activeScopes.removeAll { it.get() == null }
    }
    
    /**
     * Force garbage collection if memory is low
     */
    fun forceGCIfNeeded(context: Context) {
        if (isLowMemory(context)) {
            Log.w(TAG, "Forcing garbage collection due to low memory")
            System.gc()
        }
    }
    
    /**
     * Create a memory-aware coroutine scope
     */
    fun createMemoryAwareScope(): CoroutineScope {
        val scope = CoroutineScope(Dispatchers.Main + Job())
        registerScope(scope)
        return scope
    }
}

/**
 * Data class for memory information
 */
data class MemoryInfo(
    val totalDeviceMemory: Long,
    val availableDeviceMemory: Long,
    val isLowMemory: Boolean,
    val appMaxMemory: Long,
    val appUsedMemory: Long,
    val appFreeMemory: Long
) {
    val memoryUsagePercentage: Float
        get() = (appUsedMemory.toFloat() / appMaxMemory.toFloat()) * 100f
        
    val deviceMemoryUsagePercentage: Float
        get() = ((totalDeviceMemory - availableDeviceMemory).toFloat() / totalDeviceMemory.toFloat()) * 100f
}

/**
 * Composable for automatic memory management
 */
@Composable
fun AutoMemoryManager(
    context: Context,
    onLowMemory: (() -> Unit)? = null
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    // Check memory when app goes to background
                    if (MemoryManager.isLowMemory(context)) {
                        onLowMemory?.invoke()
                        MemoryManager.forceGCIfNeeded(context)
                    }
                }
                Lifecycle.Event.ON_DESTROY -> {
                    // Cancel scopes when destroyed
                    MemoryManager.cancelAllScopes()
                }
                else -> {}
            }
        }
        
        lifecycleOwner.lifecycle.addObserver(observer)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

/**
 * Composable scope that automatically cleans up
 */
@Composable
fun rememberManagedCoroutineScope(): CoroutineScope {
    return remember {
        MemoryManager.createMemoryAwareScope()
    }
}
