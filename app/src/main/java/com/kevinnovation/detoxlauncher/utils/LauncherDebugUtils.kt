package com.kevinnovation.detoxlauncher.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import com.kevinnovation.detoxlauncher.BuildConfig

/**
 * Debug utilities for launcher status detection
 */
object LauncherDebugUtils {
    
    private const val TAG = "LauncherDebug"
    
    /**
     * Logs detailed information about launcher detection
     */
    fun logLauncherDetectionInfo(context: Context) {
        Log.d(TAG, "=== Launcher Detection Debug Info ===")
        Log.d(TAG, "App Package: ${BuildConfig.APPLICATION_ID}")
        Log.d(TAG, "Android Version: ${Build.VERSION.SDK_INT}")
        Log.d(TAG, "Device: ${Build.MANUFACTURER} ${Build.MODEL}")
        
        // Method 1: Standard detection
        try {
            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
            }
            
            val packageManager = context.packageManager
            val resolveInfo = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
            val defaultLauncher = resolveInfo?.activityInfo?.packageName
            
            Log.d(TAG, "Method 1 (resolveActivity): $defaultLauncher")
            Log.d(TAG, "Method 1 Result: ${BuildConfig.APPLICATION_ID == defaultLauncher}")
        } catch (e: Exception) {
            Log.e(TAG, "Method 1 failed", e)
        }
        
        // Method 2: Query all activities
        try {
            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
            }
            
            val packageManager = context.packageManager
            val activities = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
            
            Log.d(TAG, "Method 2 (queryIntentActivities): Found ${activities.size} activities")
            activities.forEach { activity ->
                Log.d(TAG, "  - ${activity.activityInfo.packageName}")
            }
            
            val hasOurApp = activities.any { it.activityInfo.packageName == BuildConfig.APPLICATION_ID }
            Log.d(TAG, "Method 2 Result: $hasOurApp")
        } catch (e: Exception) {
            Log.e(TAG, "Method 2 failed", e)
        }
        
        // Method 3: RoleManager (Android 10+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            try {
                val roleManager = context.getSystemService(Context.ROLE_SERVICE) as? android.app.role.RoleManager
                val isDefaultHome = roleManager?.isRoleHeld(android.app.role.RoleManager.ROLE_HOME) ?: false
                Log.d(TAG, "Method 3 (RoleManager): $isDefaultHome")
            } catch (e: Exception) {
                Log.e(TAG, "Method 3 failed", e)
            }
        } else {
            Log.d(TAG, "Method 3 (RoleManager): Not available on Android ${Build.VERSION.SDK_INT}")
        }
        
        // Method 4: Query all home activities (no filter)
        try {
            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
            }
            
            val packageManager = context.packageManager
            val allActivities = packageManager.queryIntentActivities(intent, 0)
            
            Log.d(TAG, "Method 4 (all home activities): Found ${allActivities.size} total home activities")
            allActivities.forEach { activity ->
                Log.d(TAG, "  - ${activity.activityInfo.packageName} (enabled: ${activity.activityInfo.enabled})")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Method 4 failed", e)
        }
        
        Log.d(TAG, "=== End Debug Info ===")
    }
    
    /**
     * Logs information about potential issues with launcher detection
     */
    fun logPotentialIssues(context: Context) {
        Log.d(TAG, "=== Potential Issues Analysis ===")
        
        // Check if our app is even installed as a launcher
        try {
            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
            }
            
            val packageManager = context.packageManager
            val allActivities = packageManager.queryIntentActivities(intent, 0)
            val ourActivity = allActivities.find { it.activityInfo.packageName == BuildConfig.APPLICATION_ID }
            
            if (ourActivity == null) {
                Log.w(TAG, "ISSUE: Our app is not registered as a home activity!")
            } else {
                Log.d(TAG, "OK: Our app is registered as home activity")
                Log.d(TAG, "Activity: ${ourActivity.activityInfo.name}")
                Log.d(TAG, "Enabled: ${ourActivity.activityInfo.enabled}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check app registration", e)
        }
        
        // Check for common OEM issues
        val manufacturer = Build.MANUFACTURER.lowercase()
        when {
            manufacturer.contains("samsung") -> {
                Log.d(TAG, "Samsung device detected - may have One UI launcher issues")
            }
            manufacturer.contains("huawei") -> {
                Log.d(TAG, "Huawei device detected - may have EMUI launcher issues")
            }
            manufacturer.contains("xiaomi") -> {
                Log.d(TAG, "Xiaomi device detected - may have MIUI launcher issues")
            }
            manufacturer.contains("oppo") -> {
                Log.d(TAG, "OPPO device detected - may have ColorOS launcher issues")
            }
            manufacturer.contains("vivo") -> {
                Log.d(TAG, "Vivo device detected - may have FunTouch OS launcher issues")
            }
        }
        
        Log.d(TAG, "=== End Issues Analysis ===")
    }
}
