package com.kevinnovation.detoxlauncher.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.AlarmClock
import android.util.Log
import android.view.View
import android.view.WindowInsets
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.data.PreferencesManager

class AppUtils {

    companion object {
        fun startApp(app: AppModel, context: Context): Boolean {
            if (app.packageName == "NULL") {
                Toast.makeText(
                    context,
                    "No App selected for swipe direction. Go to settings and set your desired app there.",
                    Toast.LENGTH_LONG
                ).show()
                  return false
            }
            try {
                Log.d("AppUtils", "Starting app: ${app.name} (${app.packageName})")
                val pm = context.packageManager
                val launchIntent = pm.getLaunchIntentForPackage(app.packageName)
                if (launchIntent != null) {
                    // Add flags to ensure proper app launching behavior
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    context.startActivity(launchIntent)
                    Log.d("AppUtils", "Successfully started app: ${app.name}")
                    return true
                } else {
                    Log.w("AppUtils", "No launch intent found for: ${app.packageName}")
                    Toast.makeText(context, "Unable to start app ${app.name}", Toast.LENGTH_SHORT)
                        .show()
                    return false
                }
            } catch (e: Exception) {
                Log.e("AppUtils", "Error starting app ${app.name}: ${e.message}", e)
                Toast.makeText(context, "Unable to start app ${app.name}", Toast.LENGTH_SHORT)
                    .show()
                return false
            }
        }

        suspend fun removeApp(packageName: String, context: Context) {
            try {

                val intent = Intent(Intent.ACTION_DELETE).apply {
                    data = Uri.parse("package:$packageName")
                    // Wichtig: damit der Dialog korrekt gestartet werden kann
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
                val preferences = PreferencesManager(context)
                preferences.removeFavoriteApp(packageName)
            } catch (e: Exception) {
                Toast.makeText(context, "Unable to remove app $packageName", Toast.LENGTH_SHORT)
                    .show()
            }
        }

        fun openDefaultClockApp(context: Context) {
            val pm = context.packageManager
            Log.d("kevinx", "open clock")
            // 1) Offizieller Intent für Alarme
            try {

                val intent = Intent(AlarmClock.ACTION_SHOW_ALARMS)/*.apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }*/
                if (intent.resolveActivity(pm) != null) {
                    Log.d("kevinx", "start Activity 1")
                    context.startActivity(intent)
                    return
                }
            } catch (e: Exception) {
                Log.d("kevinx", "FAIL Open Clock option 1")
            }

            try {
                // 2) Alternativ: Kategorie für Clock
                Log.d("kevinx", "open clock 2")
                val fallbackIntent = Intent(Intent.ACTION_MAIN).apply {
                    addCategory("android.intent.category.APP_CLOCK")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                if (fallbackIntent.resolveActivity(pm) != null) {
                    Log.d("kevinx", "start Activity 2")
                    context.startActivity(fallbackIntent)
                    return
                } else {
                    throw Exception("Unable to start clock with option 2")
                }
            } catch (e: Exception) {
                Log.d("kevinx", "FAIL Open Clock option 2")
            }

            if(!startApp(AppModel("Uhr", "com.sec.android.app.clockpackage"), context)) {
                Toast.makeText(context, "Keine Uhr gefunden", Toast.LENGTH_SHORT).show()
            }

        }

        fun openDefaultCalendarApp(context: Context) {
            val pm = context.packageManager

            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_APP_CALENDAR)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            if (intent.resolveActivity(pm) != null) {
                context.startActivity(intent)
            } else {
                Toast.makeText(context, "Kein Kalender gefunden", Toast.LENGTH_SHORT).show()
            }
        }
    }
}