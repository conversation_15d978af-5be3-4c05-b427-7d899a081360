package com.kevinnovation.detoxlauncher.utils

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.os.Build
import android.util.Log
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow

// Global shadow Style
val shadowStyle = Shadow(
    color = Color.Black, offset = Offset(1f, 1f), blurRadius = 4f
)

fun getDefaultLauncherPackage(context: Context): String {
    return try {
        val intent = Intent().apply {
            action = Intent.ACTION_MAIN
            addCategory(Intent.CATEGORY_HOME)
        }

        val packageManager = context.packageManager
        val result = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)

        val packageName = result?.activityInfo?.packageName

        // Fallback für OEM-spezifische Implementierungen
        if (packageName.isNullOrEmpty() || packageName == "android") {
            // Versuche alternative Methode
            val activities = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
            activities.firstOrNull()?.activityInfo?.packageName ?: "android"
        } else {
            packageName
        }
    } catch (e: Exception) {
        Log.e("Utils", "Error getting default launcher package", e)
        "android"
    }
}



