package com.kevinnovation.detoxlauncher.utils

import android.app.Activity
import android.content.Context
import android.util.Log
import android.widget.Toast
import com.google.android.ump.ConsentInformation
import com.google.android.ump.ConsentRequestParameters
import com.google.android.ump.UserMessagingPlatform

class ConsentManager(private val context: Context) {

    private var consentInformation: ConsentInformation =
        UserMessagingPlatform.getConsentInformation(context)

    fun revokeConsent() {
        // Hole die ConsentInformation
        val consentInformation = UserMessagingPlatform.getConsentInformation(context)
        // Setze den Consent zurück
        consentInformation.reset()
        Toast.makeText(context, "Einwilligung wurde zurückgesetzt.", Toast.LENGTH_SHORT).show()
        Log.d("Consent", "Consent zurückgesetzt")
    }

    fun checkConsentAndShowForm(activity: Activity, onConsentFinished: () -> Unit) {
        val params = ConsentRequestParameters.Builder().build()

        // Consent-Status abrufen
        consentInformation.requestConsentInfoUpdate(
            activity,
            params,
            { // Falls erfolgreich, prüfen ob ein Formular verfügbar ist
                if (consentInformation.isConsentFormAvailable) {
                    showConsentForm(activity, onConsentFinished)
                } else {
                    onConsentFinished()
                }
            },
            { error -> // Fehlerhandling
                Log.e("ConsentManager", "Fehler beim Laden des Consent-Status: ${error.message}")
                onConsentFinished()
            }
        )
    }

    private fun showConsentForm(activity: Activity, onConsentFinished: () -> Unit) {
        UserMessagingPlatform.loadAndShowConsentFormIfRequired(
            activity
        ) { formError ->
            if (formError != null) {
                Log.e("ConsentManager", "Consent-Formular Fehler: ${formError.message}")
            }
            // Nach dem Anzeigen oder falls nicht notwendig, AdMob laden
            onConsentFinished()
        }
    }

    fun canRequestAds(): Boolean {
        return consentInformation.canRequestAds()
    }
}
