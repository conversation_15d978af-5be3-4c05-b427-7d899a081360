package com.kevinnovation.detoxlauncher.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter

class AppChangeReceiver(context: Context, private val onAppsChanged: () -> Unit) {

    private val appBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                Intent.ACTION_PACKAGE_ADDED,
                Intent.ACTION_PACKAGE_REMOVED -> {
                    onAppsChanged() // Trigger the refresh callback
                }
            }
        }
    }

    init {
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_PACKAGE_ADDED)
            addAction(Intent.ACTION_PACKAGE_REMOVED)
            addDataScheme("package")
        }
        context.registerReceiver(appBroadcastReceiver, intentFilter)
    }

    fun unregister(context: Context) {
        context.unregisterR<PERSON>eiver(appBroadcastReceiver)
    }
}