package com.kevinnovation.detoxlauncher.utils

import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.system.measureTimeMillis

/**
 * Performance utilities for monitoring and optimizing app performance
 */
object PerformanceUtils {
    
    private const val TAG = "PerformanceUtils"
    
    /**
     * Measure execution time of a suspend function
     */
    suspend fun <T> measureSuspend(
        operation: String,
        block: suspend () -> T
    ): T {
        var result: T
        val time = measureTimeMillis {
            result = block()
        }
        Log.d(TAG, "$operation took ${time}ms")
        return result
    }
    
    /**
     * Execute heavy operation on IO dispatcher
     */
    suspend fun <T> executeOnIO(
        block: suspend () -> T
    ): T = withContext(Dispatchers.IO) {
        block()
    }

    /**
     * Execute computation on Default dispatcher
     */
    suspend fun <T> executeOnDefault(
        block: suspend () -> T
    ): T = withContext(Dispatchers.Default) {
        block()
    }
    
    /**
     * Log memory usage
     */
    fun logMemoryUsage(tag: String) {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val availableMemory = maxMemory - usedMemory
        
        Log.d(TAG, "$tag - Memory Usage:")
        Log.d(TAG, "  Used: ${usedMemory / 1024 / 1024}MB")
        Log.d(TAG, "  Available: ${availableMemory / 1024 / 1024}MB")
        Log.d(TAG, "  Max: ${maxMemory / 1024 / 1024}MB")
    }
    
    /**
     * Suggest garbage collection (for debugging only)
     */
    fun suggestGC(reason: String) {
        Log.d(TAG, "Suggesting GC: $reason")
        System.gc()
    }
}

/**
 * Composable for performance monitoring
 */
@Composable
fun PerformanceMonitor(
    screenName: String,
    onPerformanceIssue: ((String) -> Unit)? = null
) {
    LaunchedEffect(screenName) {
        val startTime = System.currentTimeMillis()
        Log.d("PerformanceMonitor", "Screen $screenName started composition")
        
        // Monitor for composition time
        val compositionTime = System.currentTimeMillis() - startTime
        if (compositionTime > 100) { // If composition takes more than 100ms
            val message = "Slow composition detected in $screenName: ${compositionTime}ms"
            Log.w("PerformanceMonitor", message)
            onPerformanceIssue?.invoke(message)
        }
    }
}

/**
 * Remember expensive computation with caching
 */
@Composable
fun <T> rememberExpensive(
    key: Any?,
    computation: () -> T
): T {
    return remember(key) {
        val startTime = System.currentTimeMillis()
        val result = computation()
        val duration = System.currentTimeMillis() - startTime
        if (duration > 50) {
            Log.w("PerformanceUtils", "Expensive computation took ${duration}ms")
        }
        result
    }
}
