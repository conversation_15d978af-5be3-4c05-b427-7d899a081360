package com.kevinnovation.detoxlauncher.utils

import android.content.Context
import android.util.Log
import androidx.compose.ui.graphics.Color
import androidx.core.graphics.ColorUtils
import com.kevinnovation.detoxlauncher.BuildConfig
import com.kevinnovation.detoxlauncher.data.AppModel
import java.text.Collator
import java.util.Locale
import kotlin.math.max

fun Context.isDefaultLauncher(): <PERSON><PERSON><PERSON> {
    return try {
        val launcherPackageName = getDefaultLauncherPackage(this)
        Log.d("kevinx", "${BuildConfig.APPLICATION_ID} = $launcherPackageName")
        val isDefault = BuildConfig.APPLICATION_ID == launcherPackageName

        // Zusätzliche Überprüfung für Android 10+ (API 29+)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            val roleManager = getSystemService(Context.ROLE_SERVICE) as? android.app.role.RoleManager
            roleManager?.let { rm ->
                val isDefaultHome = rm.isRoleHeld(android.app.role.RoleManager.ROLE_HOME)
                Log.d("kevinx", "RoleManager check: $isDefaultHome")
                return isDefaultHome
            }
        }

        isDefault
    } catch (e: Exception) {
        Log.e("kevinx", "Error checking default launcher status", e)
        false
    }
}


// Sort chars depending on language
fun List<AppModel>.sortByLocale(): List<AppModel> {
    return this.sortedWith { c1, c2 ->
        Collator.getInstance(Locale.getDefault()).compare(c1.name, c2.name)
    }
}

fun Set<Char>.sortByLocale(): List<Char> {
    return this.sortedWith { c1, c2 ->
        Collator.getInstance(Locale.getDefault()).compare(c1.toString(), c2.toString())
    }
}

fun Color.darken(amount: Float = 0.1f): Color {
    val hsl = FloatArray(3)
    ColorUtils.RGBToHSL(
        (this.red   * 255).toInt(),
        (this.green * 255).toInt(),
        (this.blue  * 255).toInt(),
        hsl
    )
    hsl[2] = max(0f, hsl[2] - amount)  // nicht unter 0 fallen

    val darkerArgb = ColorUtils.HSLToColor(hsl)
    return Color(darkerArgb)
}