package com.kevinnovation.detoxlauncher.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.kevinnovation.detoxlauncher.BuildConfig
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Manages launcher status detection and monitoring across different Android versions
 */
class LauncherStatusManager(private val context: Context) {
    
    private val _isDefaultLauncher = MutableStateFlow(false)
    val isDefaultLauncher: StateFlow<Boolean> = _isDefaultLauncher.asStateFlow()

    private var isReceiverRegistered = false
    private val scope = CoroutineScope(Dispatchers.IO)
    private var periodicCheckJob: Job? = null
    
    // Broadcast receiver for package changes that might affect default launcher
    private val packageChangeReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_PACKAGE_ADDED,
                Intent.ACTION_PACKAGE_REMOVED,
                Intent.ACTION_PACKAGE_CHANGED,
                Intent.ACTION_PACKAGE_REPLACED -> {
                    Log.d("LauncherStatus", "Package change detected, checking launcher status")
                    checkLauncherStatus()
                }
            }
        }
    }
    
    init {
        checkLauncherStatus()
        registerPackageChangeReceiver()
        startPeriodicCheck()
    }
    
    /**
     * Checks if this app is set as the default launcher
     * Uses multiple methods for better compatibility across Android versions
     */
    fun checkLauncherStatus(): Boolean {
        val isDefault = when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                checkLauncherStatusApi29Plus() ?: checkLauncherStatusLegacy()
            }
            else -> checkLauncherStatusLegacy()
        }
        
        Log.d("LauncherStatus", "Launcher status check result: $isDefault")
        _isDefaultLauncher.value = isDefault
        return isDefault
    }
    
    /**
     * Check launcher status using RoleManager (Android 10+)
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun checkLauncherStatusApi29Plus(): Boolean? {
        return try {
            val roleManager = context.getSystemService(Context.ROLE_SERVICE) as? android.app.role.RoleManager
            roleManager?.isRoleHeld(android.app.role.RoleManager.ROLE_HOME)
        } catch (e: Exception) {
            Log.w("LauncherStatus", "RoleManager check failed", e)
            null
        }
    }
    
    /**
     * Legacy method for checking launcher status (pre-Android 10)
     */
    private fun checkLauncherStatusLegacy(): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
            }
            
            val packageManager = context.packageManager
            val resolveInfo = packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY)
            
            val defaultLauncherPackage = resolveInfo?.activityInfo?.packageName
            Log.d("LauncherStatus", "Default launcher package: $defaultLauncherPackage")
            
            BuildConfig.APPLICATION_ID == defaultLauncherPackage
        } catch (e: Exception) {
            Log.e("LauncherStatus", "Legacy launcher check failed", e)
            false
        }
    }
    
    /**
     * Alternative method using queryIntentActivities for better OEM compatibility
     */
    private fun checkLauncherStatusAlternative(): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
            }
            
            val packageManager = context.packageManager
            val activities = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
            
            activities.any { it.activityInfo.packageName == BuildConfig.APPLICATION_ID }
        } catch (e: Exception) {
            Log.e("LauncherStatus", "Alternative launcher check failed", e)
            false
        }
    }
    
    /**
     * Comprehensive check using multiple methods for maximum reliability
     */
    fun checkLauncherStatusComprehensive(): Boolean {
        val results = mutableListOf<Boolean>()
        
        // Method 1: Standard check
        results.add(checkLauncherStatusLegacy())
        
        // Method 2: Alternative check
        results.add(checkLauncherStatusAlternative())
        
        // Method 3: RoleManager (Android 10+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            checkLauncherStatusApi29Plus()?.let { results.add(it) }
        }
        
        // Return true if any method confirms we're the default launcher
        val isDefault = results.any { it }
        
        Log.d("LauncherStatus", "Comprehensive check results: $results, final: $isDefault")
        _isDefaultLauncher.value = isDefault
        return isDefault
    }
    
    private fun registerPackageChangeReceiver() {
        if (!isReceiverRegistered) {
            try {
                val filter = IntentFilter().apply {
                    addAction(Intent.ACTION_PACKAGE_ADDED)
                    addAction(Intent.ACTION_PACKAGE_REMOVED)
                    addAction(Intent.ACTION_PACKAGE_CHANGED)
                    addAction(Intent.ACTION_PACKAGE_REPLACED)
                    addDataScheme("package")
                }
                
                context.registerReceiver(packageChangeReceiver, filter)
                isReceiverRegistered = true
                Log.d("LauncherStatus", "Package change receiver registered")
            } catch (e: Exception) {
                Log.e("LauncherStatus", "Failed to register package change receiver", e)
            }
        }
    }
    
    private fun startPeriodicCheck() {
        periodicCheckJob = scope.launch {
            while (true) {
                delay(30000) // Check every 30 seconds
                checkLauncherStatus()
            }
        }
    }

    fun unregisterReceiver() {
        periodicCheckJob?.cancel()

        if (isReceiverRegistered) {
            try {
                context.unregisterReceiver(packageChangeReceiver)
                isReceiverRegistered = false
                Log.d("LauncherStatus", "Package change receiver unregistered")
            } catch (e: Exception) {
                Log.e("LauncherStatus", "Failed to unregister package change receiver", e)
            }
        }
    }
    
    /**
     * Force refresh the launcher status
     */
    fun refresh() {
        checkLauncherStatusComprehensive()
    }

    /**
     * Enable debug logging for troubleshooting
     */
    fun enableDebugLogging() {
        LauncherDebugUtils.logLauncherDetectionInfo(context)
        LauncherDebugUtils.logPotentialIssues(context)
    }
}
