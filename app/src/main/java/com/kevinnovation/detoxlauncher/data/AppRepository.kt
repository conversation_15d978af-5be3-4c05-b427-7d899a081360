package com.kevinnovation.detoxlauncher.data

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import android.util.Log
import com.kevinnovation.detoxlauncher.utils.PerformanceUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext

data class AppModel(
    val name: String,
    val packageName: String,
    val customName: String? = null
) {

    companion object {
        fun fromString(stringTxt: String): AppModel {
            val split = stringTxt.split(";")
            return when (split.size) {
                2 -> AppModel(
                    name = split[0],
                    packageName = split[1]
                )
                3 -> AppModel(
                    name = split[0],
                    packageName = split[1],
                    customName = split[2]
                )
                else -> throw RuntimeException("Cannot deserialize AppName $stringTxt")
            }
        }
    }

    override fun toString(): String {
        return if (customName != null) {
            "$name;$packageName;$customName"
        } else {
            "$name;$packageName"
        }
    }

    // Gibt den anzuzeigenden Namen zurück (benutzerdefiniert oder Standard)
    fun getDisplayName(): String {
        return customName ?: name
    }
}

fun getAppIconBitmap(context: Context, packageName: String): ImageBitmap? {
    val drawable: Drawable = context.packageManager.getApplicationIcon(packageName)
    return try {
        (drawable as BitmapDrawable).bitmap.asImageBitmap()
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

class AppRepository(private val context: Context) {
    // Cache for installed apps to improve performance
    private var cachedApps: List<AppModel>? = null
    private var lastCacheTime: Long = 0
    private val CACHE_VALID_TIME = 60 * 1000 // 60 seconds cache validity

    // Optimized function that provides a Flow of installed apps with enhanced caching
    fun getInstalledAppsFlow(): Flow<List<AppModel>> = flow {
        try {
            // Check if we have a valid cache
            val currentTime = System.currentTimeMillis()
            if (cachedApps != null && (currentTime - lastCacheTime < CACHE_VALID_TIME)) {
                Log.d("AppRepository", "Using cached apps list")
                emit(cachedApps!!)
                return@flow
            }

            // No valid cache, load apps from package manager with performance monitoring
            val apps = PerformanceUtils.measureSuspend("Loading installed apps") {
                PerformanceUtils.executeOnIO {
                    getInstalledApps()
                }
            }

            // Update cache
            cachedApps = apps
            lastCacheTime = currentTime

            emit(apps)
        } catch (e: Exception) {
            Log.e("AppRepository", "Error loading apps: ${e.message}", e)
            // If we have a cache, use it even if expired in case of error
            if (cachedApps != null) {
                Log.w("AppRepository", "Using expired cache due to error")
                emit(cachedApps!!)
            } else {
                emit(emptyList())
            }
        }
    }

    // Funktion, die installierte User-Apps abruft mit Fehlerbehandlung
    @SuppressLint("QueryPermissionsNeeded")
    fun getInstalledApps(): List<AppModel> {
        try {
            val pm = context.packageManager
            val intent = Intent(Intent.ACTION_MAIN, null).apply {
                addCategory(Intent.CATEGORY_LAUNCHER) //TODO: Check CATEGORY_HOME
            }
            val resolveInfos = pm.queryIntentActivities(intent, 0)
            return resolveInfos.map { resolveInfo ->
                try {
                    val label = resolveInfo.loadLabel(pm).toString()
                    val packageName = resolveInfo.activityInfo.packageName
                    AppModel(name = label, packageName = packageName)
                } catch (e: Exception) {
                    Log.e("AppRepository", "Error processing app: ${e.message}", e)
                    null // Return null for apps that cause errors
                }
            }.filterNotNull().sortedBy { it.name.lowercase() }
        } catch (e: Exception) {
            Log.e("AppRepository", "Error getting installed apps: ${e.message}", e)
            return emptyList() // Return empty list in case of error
        }
//        val pm = context.packageManager
//        return pm.getInstalledApplications(PackageManager.GET_META_DATA)
//            .filter { (it.flags and ApplicationInfo.FLAG_SYSTEM) == 0 } // Nur User-Apps
//            .map { appInfo ->
//                val label = pm.getApplicationLabel(appInfo).toString()
//                AppModel(name = label, packageName = appInfo.packageName)
//            }
//            .sortedBy { it.name.lowercase() }
    }
}
