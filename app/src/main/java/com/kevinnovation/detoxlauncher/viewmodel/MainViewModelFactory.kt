package com.kevinnovation.detoxlauncher.viewmodel

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.kevinnovation.detoxlauncher.utils.ConsentManager

class MainViewModelFactory(
    private val application: Application,
    private val consentManager: ConsentManager
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(MainViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return MainViewModel(application, consentManager) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
