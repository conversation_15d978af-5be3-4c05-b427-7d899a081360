package com.kevinnovation.detoxlauncher.ui

import android.content.Context
import android.content.Intent
import android.provider.Settings
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kevinnovation.detoxlauncher.R
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel

@Composable
fun SetDefaultLauncherScreen(viewModel: MainViewModel, context: Context) {
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    val textColor by viewModel.textColor.collectAsState()
    val textSize by viewModel.textSize.collectAsState()
    val fontWeightTexts by viewModel.fontWeightTexts
    val isDefaultLauncher by viewModel.isDefaultLauncher.collectAsState()

    // Check launcher status when screen is first composed
    LaunchedEffect(Unit) {
        viewModel.checkLauncherStatus()
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp)
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.Center
    ) {
        if (!isDefaultLauncher) {
            OutlinedButton(
                onClick = {
                    val intent = Intent(Settings.ACTION_HOME_SETTINGS)
                    context.startActivity(intent)
                    // Check status again after a short delay to catch quick changes
                    viewModel.refreshLauncherStatus()
                },
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = secondaryColor.copy(alpha = 0.5f),   // Hintergrundfarbe
//                contentColor = Color.Black       // Text/Icon-Farbe
                ), border = BorderStroke(1.dp, primaryColor)
            ) {
                Text(
                    text = stringResource(id = R.string.set_as_default_text),
                    textAlign = TextAlign.Center,
                    style = TextStyle(
                        brush = Brush.linearGradient(
                            colors = listOf(Color.Black, Color.Black)
                        ),
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Normal
                    )

                )
            }
        }
    }
}
