import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.toSize

/**
 * Datenklasse, die den „globalen“ Verlauf beschreibt (Farben, Start/End-Offset).
 */
data class GlobalGradient(
    val colors: List<Color>,
    val start: Offset,
    val end: Offset
)

/**
 * Erzeugt oder merkt sich einmalig einen globalen Verlauf, basierend auf
 * der gewünschten Gesamtabmessung (z. B. Bildschirm- oder Container-Größe).
 */
@Composable
fun rememberGlobalGradient(
    colors: List<Color>,
    totalSize: Size
): GlobalGradient {
    return remember(totalSize) {
        GlobalGradient(
            colors = colors,
            start = Offset(0f, 0f),
            end = Offset(totalSize.width, totalSize.height)
        )
    }
}

/**
 * Text, der einen Ausschnitt des „globalen“ Farbverlaufs nutzt.
 */
@Composable
fun GlobalGradientText(
    text: String,
    globalGradient: GlobalGradient,
    modifier: Modifier = Modifier,
    style: TextStyle = MaterialTheme.typography.bodyMedium
) {
    var offset by remember { mutableStateOf(Offset.Zero) }

    Box(
        modifier = modifier
            // Wir messen die Position des Textes
            .onGloballyPositioned { coords ->
                offset = coords.positionInWindow() // oder positionInRoot()
            }
    ) {
        val brush = Brush.linearGradient(
            colors = globalGradient.colors,
            start = globalGradient.start - offset,
            end = globalGradient.end - offset
        )

        Text(
            text = buildAnnotatedString {
                withStyle(style = SpanStyle(brush = brush)) {
                    append(text)
                }
            },
            style = style,
            textAlign = TextAlign.Start
        )
    }
}

/**
 * Screen mit mehreren Text-Elementen, alle nutzen
 * ein und denselben „globalen“ Verlauf.
 */
@Composable
fun ScreenWithGlobalGradient() {
    var containerSize by remember { mutableStateOf(Size.Zero) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .onGloballyPositioned { coords ->
                containerSize = coords.size.toSize()
            }
    ) {
        val globalGradient = rememberGlobalGradient(
            colors = listOf(Color.Cyan, Color.Magenta, Color.Green),
            totalSize = containerSize
        )

        Column(Modifier.padding(16.dp)) {
            GlobalGradientText(
                text = "Erster Text",
                globalGradient = globalGradient
            )
            Spacer(Modifier.height(32.dp))
            GlobalGradientText(
                text = "Zweiter Text weiter unten",
                globalGradient = globalGradient
            )
            Spacer(Modifier.height(32.dp))
            GlobalGradientText(
                text = "Dritter Text, gleiches Farbband",
                globalGradient = globalGradient
            )
            GlobalGradientText(
                text = "Dritter Text, gleiches Farbband",
                globalGradient = globalGradient
            )
            GlobalGradientText(
                text = "Dritter Text, gleiches Farbband",
                globalGradient = globalGradient
            )
            GlobalGradientText(
                text = "Dritter Text, gleiches Farbband",
                globalGradient = globalGradient
            )
            Spacer(Modifier.height(100.dp))
            GlobalGradientText(
                text = "Dritter Text, gleiches Farbband",
                globalGradient = globalGradient
            )
            Spacer(Modifier.height(100.dp))
            GlobalGradientText(
                text = "Dritter Text, gleiches Farbband",
                globalGradient = globalGradient
            )
            Spacer(Modifier.height(100.dp))
            GlobalGradientText(
                text = "Dritter Text, gleiches Farbband",
                globalGradient = globalGradient
            )
        }
    }
}

@Preview(showBackground = true, widthDp = 360, heightDp = 640)
@Composable
fun PreviewScreenWithGlobalGradient() {
    // Optional: ein Theme drumherum
    MaterialTheme {
        ScreenWithGlobalGradient()
    }
}
