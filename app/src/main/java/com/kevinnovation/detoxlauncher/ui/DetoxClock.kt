package com.kevinnovation.detoxlauncher.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.sp
import com.kevinnovation.detoxlauncher.utils.AppUtils
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel

@Composable
fun DetoxClock(viewModel: MainViewModel) {

    val context = LocalContext.current
    val currentTime by viewModel.currentTime.collectAsState()
    val currentDate by viewModel.currentDate.collectAsState()
    val batteryLevel by viewModel.batteryLevel.collectAsState()
    val fontWeightClock by viewModel.fontWeightClock
    val fontWeightDate by viewModel.fontWeightDate
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()

    // Obere Leiste (Uhrzeit, Datum, Akku)
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            modifier = Modifier
                .clickable {
                    AppUtils.openDefaultClockApp(context)
                },
            text = currentTime,
            style = TextStyle(
                brush = Brush.linearGradient(
                    colors = listOf(primaryColor, secondaryColor)
                ),
                fontSize = 90.sp,
                fontWeight = fontWeightClock,
//                shadow = shadowStyle
            ),
            color = Color.Black
        )
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            modifier = Modifier
                .clickable {
                    AppUtils.openDefaultCalendarApp(context)
                },
            text = "$currentDate • $batteryLevel",
            style = TextStyle(
                brush = Brush.linearGradient(
                    colors = listOf(primaryColor, secondaryColor)
                ),
                fontSize = 20.sp,
                fontWeight = fontWeightDate
            )

        )
    }
}