package com.kevinnovation.detoxlauncher.ui.gestures

import android.util.Log
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.awaitTouchSlopOrCancellation
import androidx.compose.foundation.gestures.detectDragGestures

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.PointerInputScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.coroutineScope
import kotlin.math.abs
import kotlin.math.absoluteValue

/**
 * Gesture configuration for swipe detection
 */
data class GestureConfig(
    val swipeThreshold: Float = 100f,
    val velocityThreshold: Float = 300f,
    val timeoutMs: Long = 500L,
    val enableLogging: Boolean = false
)

/**
 * Gesture events
 */
sealed class GestureEvent {
    object SwipeUp : GestureEvent()
    object SwipeDown : GestureEvent()
    object SwipeLeft : GestureEvent()
    object SwipeRight : GestureEvent()
    object LongPress : GestureEvent()
    object Tap : GestureEvent()
}

/**
 * Gesture callbacks
 */
data class GestureCallbacks(
    val onSwipeUp: ((isLeft: Boolean) -> Unit)? = null,
    val onSwipeDown: (() -> Unit)? = null,
    val onSwipeLeft: (() -> Unit)? = null,
    val onSwipeRight: (() -> Unit)? = null,
    val onLongPress: (() -> Unit)? = null,
    val onTap: (() -> Unit)? = null
)

/**
 * Enhanced gesture handler with improved performance and reliability
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun GestureHandler(
    modifier: Modifier = Modifier,
    config: GestureConfig = GestureConfig(),
    callbacks: GestureCallbacks,
    content: @Composable () -> Unit
) {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    val screenWidth = with(density) { configuration.screenWidthDp.dp.toPx() }

    var gestureStartPosition by remember { mutableStateOf(Offset.Zero) }

    androidx.compose.foundation.layout.Box(
        modifier = modifier
            .fillMaxSize()
            .combinedClickable(
                onClick = {
                    callbacks.onTap?.invoke()
                    if (config.enableLogging) {
                        Log.d("GestureHandler", "Tap detected")
                    }
                },
                onLongClick = {
                    callbacks.onLongPress?.invoke()
                    if (config.enableLogging) {
                        Log.d("GestureHandler", "Long press detected")
                    }
                }
            )
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { offset ->
                        gestureStartPosition = offset
                        if (config.enableLogging) {
                            Log.d("GestureHandler", "Gesture started at: $offset")
                        }
                    },
                    onDragEnd = {
                        // Gesture ended
                    },
                    onDrag = { change, dragAmount ->
                        val distance = dragAmount.getDistance()

                        if (distance > config.swipeThreshold) {
                            val direction = determineSwipeDirection(dragAmount)

                            when (direction) {
                                SwipeDirection.UP -> {
                                    val isLeft = gestureStartPosition.x < screenWidth / 2
                                    // Update PreferencesManager.IS_LEFT for compatibility
                                    com.kevinnovation.detoxlauncher.data.PreferencesManager.IS_LEFT = isLeft

                                    callbacks.onSwipeUp?.invoke(isLeft)
                                    if (config.enableLogging) {
                                        Log.d("GestureHandler", "Swipe up detected (left side: $isLeft)")
                                    }
                                }
                                SwipeDirection.DOWN -> {
                                    callbacks.onSwipeDown?.invoke()
                                    if (config.enableLogging) {
                                        Log.d("GestureHandler", "Swipe down detected")
                                    }
                                }
                                SwipeDirection.LEFT -> {
                                    callbacks.onSwipeLeft?.invoke()
                                    if (config.enableLogging) {
                                        Log.d("GestureHandler", "Swipe left detected")
                                    }
                                }
                                SwipeDirection.RIGHT -> {
                                    callbacks.onSwipeRight?.invoke()
                                    if (config.enableLogging) {
                                        Log.d("GestureHandler", "Swipe right detected")
                                    }
                                }
                            }
                        }
                    }
                )
            }
    ) {
        content()
    }
}

/**
 * Swipe direction enum
 */
private enum class SwipeDirection {
    UP, DOWN, LEFT, RIGHT
}

/**
 * Determine swipe direction from offset
 */
private fun determineSwipeDirection(offset: Offset): SwipeDirection {
    val horizontalDistance = abs(offset.x)
    val verticalDistance = abs(offset.y)
    
    return if (horizontalDistance > verticalDistance) {
        if (offset.x > 0) SwipeDirection.RIGHT else SwipeDirection.LEFT
    } else {
        if (offset.y > 0) SwipeDirection.DOWN else SwipeDirection.UP
    }
}
