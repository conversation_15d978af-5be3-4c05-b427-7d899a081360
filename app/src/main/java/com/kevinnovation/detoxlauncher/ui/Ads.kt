package com.kevinnovation.detoxlauncher.ui

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.OutlinedCard
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.bumptech.glide.Glide
import com.google.android.gms.ads.nativead.NativeAdView
import com.kevinnovation.detoxlauncher.R
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel

@Composable
fun NativeAdViewCompose(viewModel: MainViewModel) {
    val context = LocalContext.current
    val textColor by viewModel.textColor.collectAsState()
    val nativeAd = viewModel.nativeAd

//    // Lade die Native Ad jedesmal neu
//    LaunchedEffect(Unit) {
//        val adLoader = AdLoader.Builder(context, adUnitId)
//            .forNativeAd { ad -> nativeAd = ad }
//            .withAdListener(object : AdListener() {
//                override fun onAdFailedToLoad(error: LoadAdError) {
//                    nativeAd = null
//                }
//            })
//            .build()
//
//        adLoader.loadAd(AdRequest.Builder().build())
//    }

    OutlinedCard(
        shape = RoundedCornerShape(4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.0f),
        ),
//        border = BorderStroke(0.dp, textColor),
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp) // 16 bisher
            .height(58.dp)
            .graphicsLayer(clip = false)
    ) {
        nativeAd?.let { ad ->
            AndroidView(
                factory = { ctx ->
                    // Inflate das XML-Layout, das ein NativeAdView enthält
                    val inflater = LayoutInflater.from(ctx)
                    val adView = inflater.inflate(R.layout.native_ad_layout, null) as NativeAdView

                    // Deaktiviere Clipping auch im NativeAdView-Container
                    adView.clipToPadding = false
                    adView.clipChildren = false

                    // Optimierung: Setze einmalig die View-Eigenschaften
                    adView.isClickable = true
                    adView.isFocusable = true

                    // Hole die einzelnen Asset-Views aus dem Layout
                    val headlineView = adView.findViewById<TextView>(R.id.ad_headline)
                    val bodyView = adView.findViewById<TextView>(R.id.ad_body)
                    val ctaButton = adView.findViewById<Button>(R.id.ad_call_to_action)
                    val imageView = adView.findViewById<ImageView>(R.id.ad_image)
                    // Optional: Ad-Label (Anzeige) etc.

                    // Setze die Inhalte der Ad
                    headlineView.text = ad.headline
                    adView.headlineView = headlineView

                    if (!ad.body.isNullOrEmpty()) {
                        bodyView.text = ad.body
                        bodyView.visibility = View.VISIBLE
                        adView.bodyView = bodyView
                    } else {
                        bodyView.visibility = View.GONE
                    }

                    if (!ad.callToAction.isNullOrEmpty()) {
                        ctaButton.text = ad.callToAction
                        ctaButton.visibility = View.VISIBLE
                        adView.callToActionView = ctaButton // Wichtig für die Klick-Registrierung
                    } else {
                        ctaButton.visibility = View.GONE
                    }

                    // Ad-Bild laden - prüfe verschiedene Bildquellen
                    val hasImage = ad.images?.firstOrNull()?.let { image ->
                        try {
                            Glide.with(ctx).load(image.drawable).into(imageView)
                            imageView.visibility = View.VISIBLE
                            true
                        } catch (e: Exception) {
                            Log.w("kevinxads", "Failed to load ad image: ${e.message}")
                            false
                        }
                    } ?: false

                    // Fallback: Prüfe Icon falls kein Hauptbild verfügbar
                    if (!hasImage) {
                        ad.icon?.let { icon ->
                            try {
                                Glide.with(ctx).load(icon.drawable).into(imageView)
                                imageView.visibility = View.VISIBLE
                                Log.d("kevinxads", "Using ad icon as fallback image")
                            } catch (e: Exception) {
                                imageView.visibility = View.GONE
                                Log.d("kevinxads", "No image available for ad")
                            }
                        } ?: run {
                            imageView.visibility = View.GONE
                            Log.d("kevinxads", "No image or icon available for ad")
                        }
                    }

                    // Binde die Native Ad an das NativeAdView
                    adView.setNativeAd(ad)
                    adView
                },
                update = { adView ->
                    // Falls sich der Text-Farbwert ändert, update den Headline-Text
                    adView.findViewById<TextView>(R.id.ad_headline)
                        ?.setTextColor(textColor.toArgb())
                },
                modifier = Modifier.fillMaxWidth().graphicsLayer(clip = false)
            )
        }
    }
}
