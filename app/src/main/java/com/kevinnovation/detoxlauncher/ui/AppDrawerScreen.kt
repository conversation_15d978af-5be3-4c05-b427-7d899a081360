package com.kevinnovation.detoxlauncher.ui

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsIgnoringVisibility
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Face
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material.icons.outlined.Clear
import androidx.compose.material.icons.outlined.Delete
import androidx.compose.material.icons.outlined.Edit
import androidx.compose.material.icons.outlined.Favorite
import androidx.compose.material.icons.outlined.VisibilityOff
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.foundation.border
import androidx.compose.ui.window.Dialog
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import kotlinx.coroutines.delay
import com.kevinnovation.detoxlauncher.R
import com.kevinnovation.detoxlauncher.data.AppModel
import com.kevinnovation.detoxlauncher.data.PreferencesManager
import com.kevinnovation.detoxlauncher.utils.AppUtils
import com.kevinnovation.detoxlauncher.utils.shadowStyle
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel
import kotlinx.coroutines.launch

enum class DRAWER_MODE {
    OPEN, CHOOSE_SWIPE_LEFT, CHOOSE_SWIPE_RIGHT
}

@OptIn(ExperimentalFoundationApi::class, ExperimentalLayoutApi::class)
@Composable
fun AppDrawerScreen(
    viewModel: MainViewModel,
    drawerMode: DRAWER_MODE,
    onSelectedApp: (AppModel) -> Unit,
    onBack: () -> Unit,
    onSwipeLeft: () -> Unit,
    onAddToFavorites: suspend (String) -> Unit,
    onRemoveFromFavorites: suspend (String) -> Unit,
    isFavorite: (String) -> Boolean,
) {
    val context = LocalContext.current
    val allApps by viewModel.allApps.collectAsState()
    val scope = rememberCoroutineScope()
    val listState = rememberLazyListState()
    val fontWeight by viewModel.fontWeightTexts
    val textSpaceing by viewModel.textSpacing.collectAsState()
    val textSize by viewModel.textSize.collectAsState()
    val textColor by viewModel.textColor.collectAsState()
    val nativeAd = viewModel.nativeAd

    // Lade Anzeige beim Öffnen des AppDrawers
    LaunchedEffect(Unit) {
        viewModel.loadAdOnDrawerOpen()
    }

    // Einstellungen für modifizierte Apps
    val highlightModifiedApps by viewModel.highlightModifiedApps.collectAsState()

    // Hilfsfunktion, um sp in dp umzuwandeln
    val density = LocalDensity.current

    // Für das Kontextmenü
    var showMenuForApp by remember { mutableStateOf<AppModel?>(null) }

    // Für das Umbenennen
    var appToRename by remember { mutableStateOf<AppModel?>(null) }
    var newAppName by remember { mutableStateOf("") }
    var showRenameDialog by remember { mutableStateOf(false) }

    // Suchtext im State halten
    var searchQuery by remember { mutableStateOf("") }

    // Editiermodus
    val editModeActive = viewModel.showHiddenApps.collectAsState().value || viewModel.highlightModifiedApps.collectAsState().value
    val editModeIcon = if (editModeActive) Icons.Default.VisibilityOff else Icons.Default.Visibility
    val editModeColor = if (editModeActive) Color(0xFF4CAF50) else Color.Gray

    // Gefilterte Apps (nach eingegebener Suche)
    val filteredApps = remember(searchQuery, allApps) {
        if (searchQuery.isBlank()) {
            allApps
        } else {
            val filteredApps = allApps
                .mapValues { (_, appList) ->
                    appList.filter { app ->
                        app.name.contains(searchQuery, ignoreCase = true)
                    }
                }
                .filterValues {
                    it.isNotEmpty()
                }

            // Check result size
            val resultSize = filteredApps.values.sumOf { it.size }

            // Open app if size is exactly 1
            if(resultSize == 1) {
                AppUtils.startApp(filteredApps.values.flatten()[0], context)
            }
            filteredApps
        }
    }

    // Mapping von Buchstaben zu Start-Indizes
    val letterToIndexMap = remember(allApps) {
        val map = mutableMapOf<Char, Int>()
        var currentIndex = 0
        allApps.keys.sorted().forEach { letter ->
            map[letter] = currentIndex
            currentIndex += 1 // Header
            currentIndex += allApps[letter]?.size ?: 0 // Anzahl der Apps unter diesem Buchstaben
        }
        map
    }

    // Erstelle eine NestedScrollConnection
    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(
                available: Offset,
                source: NestedScrollSource
            ): Offset {
                // Überprüfen, ob die Liste ganz oben ist und ein Swipe-Down stattfindet
                if (listState.firstVisibleItemIndex == 0 && listState.firstVisibleItemScrollOffset == 0) {
                    if (available.y > 0) { // Swipe-Down erkannt
                        onBack()
                        return Offset.Zero // Konsumiere das Scroll-Event
                    }
                }
                return Offset.Zero
            }
        }
    }

    // Main App Drawer
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.Black.copy(0.4f))
            .padding(WindowInsets.systemBarsIgnoringVisibility.asPaddingValues())
            .nestedScroll(nestedScrollConnection)
    ) {
        Column {
            Row(modifier = Modifier
                .fillMaxWidth()
                .height(58.dp)) {

                if (nativeAd != null) {
                    NativeAdViewCompose(viewModel = viewModel)
                }
            }
            Row(modifier = Modifier
                .fillMaxWidth()
                .nestedScroll(nestedScrollConnection)
            ) {
                // Suchfeld oben platzieren
                SearchBar(
                    viewModel = viewModel,
                    query = searchQuery,
                    onValueChange = { newValue -> searchQuery = newValue }
                )
            }

            val (paddingStart, rowOffset) = if (PreferencesManager.IS_LEFT) {
                0.dp to 80.dp
            } else {
                16.dp to 0.dp
            }

            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .nestedScroll(nestedScrollConnection)
                    .padding(start = paddingStart, end = 80.dp)
                    .offset(rowOffset)
            ) {
                val screenPadding = LocalConfiguration.current.screenHeightDp.dp / 4
                // Liste der Apps
                LazyColumn(
                    state = listState,
                    contentPadding = PaddingValues(
                        top = screenPadding,
                        bottom = screenPadding
                    ),
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight()
                ) {
                    // Editiermodus-Umschalter am Anfang der Liste
                    item {

                        // Big Letter
                        Icon(
                            editModeIcon,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.padding(vertical = 8.dp).clickable {
                                // Umschalten des Editiermodus
                                if (editModeActive) {
                                    // Editiermodus ausschalten
                                    viewModel.setShowHiddenApps(false)
                                    viewModel.setHighlightModifiedApps(false)
                                } else {
                                    // Editiermodus einschalten
                                    viewModel.setShowHiddenApps(true)
                                    viewModel.setHighlightModifiedApps(true)
                                }
                            },
                        )
                    }
                    filteredApps.keys.sorted().forEach { letter ->
                        val appsForLetter = filteredApps[letter] ?: emptyList()
                        item {
                            // Big Letter
                            Text(
                                text = letter.toString(),
                                style = TextStyle(
//                                brush = Brush.horizontalGradient(
//                                    colors = listOf(Color.White, Color.White)
//                                ),
                                    color = textColor,
                                    fontSize = 35.sp,
                                    fontWeight = FontWeight(
                                        minOf(
                                            fontWeight.weight + FontWeight.W200.weight,
                                            900
                                        )
                                    )
                                ),
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }
                        items(appsForLetter) { app ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(Color.Transparent)
                                    .combinedClickable(
                                        onClick = {
                                            onSelectedApp(app)
                                        },
                                        onLongClick = {
                                            // Kontextmenü zeigen
                                            showMenuForApp = app
                                        }
                                    )
                            ) {
                                // DropdownMenu für Add/Remove Favorites
                                if (showMenuForApp?.equals(app) == true) {
                                    DropdownMenu(
                                        expanded = true,
                                        onDismissRequest = { showMenuForApp = null },
                                        modifier = Modifier
                                            .background(
                                                brush = Brush.linearGradient(
                                                    colors = listOf(
                                                        Color(65, 255, 100, 255),
                                                        Color(0, 120, 208, 255)
                                                    )
                                                )
                                            )
                                    ) {
                                        // Favorite/Unfavorite option based on current status
                                        if (isFavorite(app.packageName)) {
                                            DropdownMenuItem(
                                                leadingIcon = {
                                                    Icon(
                                                        Icons.Outlined.Clear,
                                                        contentDescription = null,
                                                        tint = Color.White
                                                    )
                                                },
                                                text = {
                                                    Text(
                                                        text = stringResource(id = R.string.global_remove_from_favorites),
                                                        fontWeight = FontWeight.Normal,
                                                        color = Color.White,
                                                        fontSize = maxOf(textSize - 15, 15).sp
                                                    )
                                                },
                                                onClick = {
                                                    scope.launch {
                                                        onRemoveFromFavorites(app.packageName)
                                                        showMenuForApp = null
                                                    }
                                                }
                                            )
                                        } else {
                                            DropdownMenuItem(
                                                leadingIcon = {
                                                    Icon(
                                                        Icons.Outlined.Favorite,
                                                        contentDescription = null,
                                                        tint = Color.White
                                                    )
                                                },
                                                text = {
                                                    Text(
                                                        text = stringResource(id = R.string.global_add_to_favorites),
                                                        color = Color.White,
                                                        fontWeight = FontWeight.Normal,
                                                        fontSize = maxOf(textSize - 15, 15).sp
                                                    )
                                                },
                                                onClick = {
                                                    scope.launch {
                                                        onAddToFavorites(app.packageName)
                                                        showMenuForApp = null
                                                    }
                                                }
                                            )
                                        }

                                        // Uninstall option - always available
                                        DropdownMenuItem(
                                            leadingIcon = {
                                                Icon(
                                                    Icons.Outlined.Delete,
                                                    contentDescription = null,
                                                    tint = Color.White
                                                )
                                            },
                                            text = {
                                                Text(
                                                    text = stringResource(id = R.string.global_app_uninstall),
                                                    fontWeight = FontWeight.Normal,
                                                    color = Color.White,
                                                    fontSize = maxOf(textSize - 15, 15).sp
                                                )
                                            },
                                            onClick = {
                                                scope.launch {
                                                    AppUtils.removeApp(
                                                        app.packageName,
                                                        context
                                                    )
                                                    showMenuForApp = null
                                                }
                                            }
                                        )

                                        // Option zum Umbenennen - always available
                                        DropdownMenuItem(
                                            leadingIcon = {
                                                Icon(
                                                    Icons.Outlined.Edit,
                                                    contentDescription = null,
                                                    tint = Color.White
                                                )
                                            },
                                            text = {
                                                Text(
                                                    text = stringResource(id = R.string.global_rename_app_menu),
                                                    fontWeight = FontWeight.Normal,
                                                    color = Color.White,
                                                    fontSize = maxOf(textSize - 15, 15).sp
                                                )
                                            },
                                            onClick = {
                                                // Dialog zum Umbenennen anzeigen
                                                appToRename = app
                                                newAppName = app.customName ?: app.name
                                                showMenuForApp = null
                                                showRenameDialog = true
                                            }
                                        )

                                        // Option zum Ausblenden/Einblenden - always available
                                        val isHidden = viewModel.isAppHidden(app.packageName)
                                        DropdownMenuItem(
                                            leadingIcon = {
                                                Icon(
                                                    Icons.Outlined.VisibilityOff,
                                                    contentDescription = null,
                                                    tint = Color.White
                                                )
                                            },
                                            text = {
                                                Text(
                                                    text = stringResource(
                                                        id = if (isHidden) R.string.global_unhide_app_menu else R.string.global_hide_app_menu
                                                    ),
                                                    fontWeight = FontWeight.Normal,
                                                    color = Color.White,
                                                    fontSize = maxOf(textSize - 15, 15).sp
                                                )
                                            },
                                            onClick = {
                                                if (isHidden) {
                                                    viewModel.unhideApp(app.packageName)
                                                } else {
                                                    viewModel.hideApp(app.packageName)
                                                }
                                                showMenuForApp = null
                                            }
                                        )
                                    }
                                }
                                // Bestimmen der Hintergrundfarbe basierend auf dem App-Status
                                val backgroundColor = when {
                                    app.customName != null && viewModel.isAppHidden(app.packageName) -> Color(0x33FF5722) // Orange-transparent für umbenannte und ausgeblendete Apps
                                    app.customName != null && editModeActive -> Color(0x334CAF50) // Grün-transparent für umbenannte Apps
                                    viewModel.isAppHidden(app.packageName) -> Color(0x33F44336) // Rot-transparent für ausgeblendete Apps
                                    else -> Color.Transparent
                                }

                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .padding(
                                            5.dp,
                                            top = textSpaceing.dp,
                                            bottom = textSpaceing.dp
                                        )
                                        .background(
                                            color = backgroundColor,
                                            shape = RoundedCornerShape(4.dp)
                                        )
                                        .padding(horizontal = 4.dp, vertical = 2.dp)
                                ) {
                                    // Wenn die App modifiziert ist und die Hervorhebung aktiviert ist, zeige ein Symbol an
                                    if (highlightModifiedApps) {
                                        // Prüfen, ob die App umbenannt wurde
                                        if (app.customName != null) {
                                            // Symbol für umbenannte App
                                            Icon(
                                                imageVector = Icons.Default.Edit,
                                                contentDescription = "Umbenannt",
                                                tint = Color(0xFF4CAF50), // Grün für umbenannte Apps
                                                modifier = Modifier
                                                    .size(textSize.dp)
                                                    .padding(end = 4.dp)
                                            )
                                        }

                                        // Prüfen, ob die App ausgeblendet ist
                                        if (viewModel.isAppHidden(app.packageName)) {
                                            // Symbol für ausgeblendete App
                                            Icon(
                                                imageVector = Icons.Default.VisibilityOff,
                                                contentDescription = "Ausgeblendet",
                                                tint = Color(0xFFF44336), // Rot für ausgeblendete Apps
                                                modifier = Modifier
                                                    .size(textSize.dp)
                                                    .padding(end = 4.dp)
                                            )
                                        }
                                    }

                                    // Normaler Text für App-Namen
                                    Text(
                                        text = app.getDisplayName(),
                                        style = TextStyle(
                                            lineHeight = textSize.sp,
                                            fontSize = textSize.sp,
                                            fontWeight = fontWeight,
                                            color = textColor,
                                            shadow = shadowStyle
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Verbesserter Dialog zum Umbenennen einer App
    if (showRenameDialog && appToRename != null) {
        ModernRenameDialog(
            app = appToRename!!,
            initialName = newAppName,
            onDismiss = { showRenameDialog = false },
            viewModel = viewModel,
            onRename = { newName ->
                if (newName.isNotBlank() && newName != appToRename!!.name) {
                    viewModel.setCustomAppName(appToRename!!.packageName, newName)
                } else if (newName == appToRename!!.name && appToRename!!.customName != null) {
                    viewModel.removeCustomAppName(appToRename!!.packageName)
                }
                showRenameDialog = false
                appToRename = null
            }
        )
    }

    // Alphabet Drawer
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        VerticalAlphabetScroller(
            letters = allApps.keys.sorted(),
            onLetterClicked = { letter ->
                val absoluteIndex = letterToIndexMap[letter] ?: 0
                Log.d("VerticalAlphabetScroller", "absoluteIndex => $absoluteIndex")
                scope.launch {
                    listState.animateScrollToItem(absoluteIndex)
                }
            },
            modifier = Modifier
                .width(80.dp)
                .align(
                    if (PreferencesManager.IS_LEFT) {
                        Alignment.BottomStart
                    } else {
                        Alignment.BottomEnd
                    }
                )
                .padding(bottom = 70.dp),
            viewModel = viewModel,
            fontWeight = fontWeight
        )
    }

    // Letter Bubble
    Box(
        modifier = Modifier
            .fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // Bubble anzeigen, wenn ein Buchstabe ausgewählt ist
        AnimatedVisibility(
            visible = viewModel.currentLetter != '*',
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            if (viewModel.currentLetter != '*') {
                BigLetter(
                    viewModel = viewModel,
                    modifier = Modifier.fillMaxSize(),
                    fontWeight = fontWeight,
                    letter = viewModel.currentLetter.toString()
                )
            }
        }
    }


}

// Einfaches Suchfeld
@Composable
fun SearchBar(
    viewModel: MainViewModel,
    query: String,
    onValueChange: (String) -> Unit
) {
    val textColor by viewModel.textColor.collectAsState()
    val textSize by viewModel.textSize.collectAsState()

    OutlinedTextField(
        value = query,
        onValueChange = onValueChange,
        singleLine = true,
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = null,
                tint = textColor,
                modifier = Modifier.size(textSize.dp)

            )
        },
        textStyle = TextStyle(fontSize = textSize.sp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = TextFieldDefaults.colors(
            cursorColor = textColor,
            focusedTextColor = textColor,
            // Set everything to transparent
            unfocusedContainerColor = Color.Transparent,
            focusedContainerColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
            focusedIndicatorColor = Color.Transparent,
            focusedLabelColor = Color.Transparent,
            unfocusedLabelColor = Color.Transparent
        )
    )
}

@Composable
fun BigLetter(
    viewModel: MainViewModel,
    modifier: Modifier,
    fontWeight: FontWeight,
    letter: String
) {
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()

    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Surface(
            shape = RoundedCornerShape(25.dp),
            color = Color.Black.copy(alpha = 0.5f),
            modifier = Modifier
                .size(200.dp)
                .align(Alignment.Center)
        ) {
            Box(contentAlignment = Alignment.Center) {
                Text(
                    text = letter,
                    textAlign = TextAlign.Center,
                    style = TextStyle(
                        brush = Brush.linearGradient(
                            colors = listOf(primaryColor, secondaryColor)
                        ),
                        fontSize = 200.sp,
                        fontWeight = fontWeight
                    )

                )
            }
        }
    }
}

@Composable
fun ModernRenameDialog(
    app: AppModel,
    initialName: String,
    viewModel: MainViewModel,
    onDismiss: () -> Unit,
    onRename: (String) -> Unit
) {
    var name by remember { mutableStateOf(initialName) }
    val focusRequester = remember { FocusRequester() }

    // Get app colors from viewModel
//    val viewModel = LocalContext.current.getActivity()?.let {
//        (it as? MainActivity)?.mainViewModel
//    }
    val primaryColor = viewModel?.primaryColor?.collectAsState()?.value ?: Color(0xFF4CAF50)
    val secondaryColor = viewModel?.secondaryColor?.collectAsState()?.value ?: Color(0xFF03DAC6)
    val textColor by viewModel.textColor.collectAsState()
    val textSize by viewModel.textSize.collectAsState()
    val fontWeightTexts by viewModel.fontWeightTexts

    // Verwende LaunchedEffect, um den Fokus zu setzen, wenn der Dialog angezeigt wird
    LaunchedEffect(Unit) {
        delay(100) // Kurze Verzögerung, um sicherzustellen, dass der Dialog vollständig gerendert ist
        focusRequester.requestFocus()
    }

    Dialog(onDismissRequest = onDismiss) {
        // Äußere Surface mit Blur-Effekt (simuliert durch Transparenz)
        Surface(
            shape = RoundedCornerShape(24.dp),
            color = Color.Transparent,
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .wrapContentHeight()
                .graphicsLayer {
                    // Leichte Unschärfe für den Blur-Effekt
                    alpha = 0.98f
                    shadowElevation = 8f
                }
        ) {
            // Innere Box mit transparentem Hintergrund - ähnlich wie SettingsCard
            Box(
                modifier = Modifier
                    .background(
                        color = Color.Black.copy(alpha = 0.2f),
                        shape = RoundedCornerShape(24.dp)
                    )
                    .border(
                        width = 1.dp,
                        color = textColor,
                        shape = RoundedCornerShape(24.dp)
                    )
            ) {
                Box(modifier = Modifier.fillMaxWidth()) {
                    // Stift-Symbol in der oberen linken Ecke
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier
                            .size(42.dp)
                            .padding(8.dp)
                            .align(Alignment.TopStart)
                    )

                    Column(modifier = Modifier.padding(horizontal = 24.dp, vertical = 16.dp)) {
                        // Titel im Stil der SettingsCard
                        Text(
                            text = stringResource(id = R.string.global_rename_app_dialog_title),
                            style = TextStyle(
                                color = textColor,
                                fontSize = textSize.sp,
                                fontWeight = fontWeightTexts,
                                shadow = shadowStyle
                            ),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 16.dp, bottom = 16.dp),
                            textAlign = TextAlign.Center,
                        )

                        Spacer(modifier = Modifier.height(20.dp))

                        // Ursprünglicher App-Name im Stil der SettingOption
                        Row(modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                text = stringResource(id = R.string.global_rename_app_dialog_original_name),
                                style = TextStyle(
                                    color = textColor,
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Normal
                                ),
                                modifier = Modifier.padding(end = 5.dp)
                            )

                            Text(
                                text = app.name,
                                style = TextStyle(
                                    color = textColor,
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight.Light
                                )
                            )
                        }

                        Spacer(modifier = Modifier.height(20.dp))

                        // Textfeld für neuen Namen mit transparentem Hintergrund
                        OutlinedTextField(
                            value = name,
                            onValueChange = { name = it },
                            label = {
                                Text(
                                    stringResource(id = R.string.global_rename_app_dialog_new_name),
                                    color = textColor
                                )
                            },
                            colors = TextFieldDefaults.colors(
                                focusedContainerColor = Color.Transparent,
                                unfocusedContainerColor = Color.Transparent,
                                focusedTextColor = textColor,
                                unfocusedTextColor = textColor,
                                focusedIndicatorColor = primaryColor,
                                unfocusedIndicatorColor = secondaryColor,
                                focusedLabelColor = primaryColor,
                                unfocusedLabelColor = textColor,
                                cursorColor = primaryColor
                            ),
                            singleLine = true,
                            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                            keyboardActions = KeyboardActions(onDone = { onRename(name) }),
                            modifier = Modifier
                                .fillMaxWidth()
                                .focusRequester(focusRequester)
                        )

                        Spacer(modifier = Modifier.height(24.dp))

                        // Buttons im Stil der SettingOption und des Kontextmenüs
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp, Alignment.End)
                        ) {
                            // Abbrechen-Button im Stil der SettingOption
                            Button(
                                onClick = onDismiss,
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color.Transparent,
                                    contentColor = Color.White
                                ),
                                modifier = Modifier
                                    .background(
                                        brush = Brush.linearGradient(
                                            colors = listOf(Color.Red, Color.Magenta)
                                        ),
                                        shape = RoundedCornerShape(8.dp)
                                    )
                            ) {
                                Text(
                                    text = stringResource(id = R.string.global_rename_app_dialog_cancel),
                                    style = TextStyle(
                                        color = textColor,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                )
                            }

                            // Speichern-Button mit Farbverlauf wie im Kontextmenü
                            Button(
                                onClick = { onRename(name) },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color.Transparent,
                                    contentColor = Color.White
                                ),
                                modifier = Modifier
                                    .background(
                                        brush = Brush.linearGradient(
                                            colors = listOf(
                                                Color(65, 255, 100, 255),
                                                Color(0, 120, 208, 255)
                                            )
                                        ),
                                        shape = RoundedCornerShape(8.dp)
                                    )
                            ) {
                                Text(
                                    text = stringResource(id = R.string.global_rename_app_dialog_save),
                                    style = TextStyle(
                                        color = Color.White,
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight.Bold
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

// Extension function to get the Activity from Context
fun Context.getActivity(): Activity? {
    var context = this
    while (context is ContextWrapper) {
        if (context is Activity) return context
        context = context.baseContext
    }
    return null
}
