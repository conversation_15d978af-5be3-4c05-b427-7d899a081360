package com.kevinnovation.detoxlauncher.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import com.kevinnovation.detoxlauncher.utils.shadowStyle
import com.kevinnovation.detoxlauncher.viewmodel.MainViewModel

@Composable
fun VerticalAlphabetScroller(
    letters: List<Char>,
    onLetterClicked: (Char) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: MainViewModel,
    fontWeight: FontWeight
) {
//    var currentLetter by remember { mutableStateOf<Char?>(null) }
    var bubbleY by remember { mutableStateOf(0f) } // Y-Position der Bubble

    val textColor by viewModel.textColor.collectAsState()

    Box(modifier = modifier) {
        // Alphabet-Zeilen mittig anordnen
        Column(
            modifier = Modifier
                .wrapContentHeight() // Beschränkt die Höhe auf den Inhalt
//                .fillMaxHeight(0.5f) // Beschränkt die Höhe auf 50% des Screens
                .pointerInput(Unit) {
                    detectVerticalDragGestures(
                        onVerticalDrag = { change, _ ->
                            val letter = getLetterAtPosition(change.position.y, size.height.toFloat(), letters)
                            if (letter != viewModel.currentLetter) {
                                viewModel.updateCurrentLetter(letter)
                                onLetterClicked(letter)
                            }
                            bubbleY = change.position.y.coerceIn(30f, size.height.toFloat() - 30f) // Clamp Y-Position
                        },
                        onDragEnd = {
                            // Drag beendet, entferne die aktuelle Buchstabenanzeige
                            viewModel.updateCurrentLetter('*')
                        }
                    )
                },
            verticalArrangement = Arrangement.Center, // Zentriert die Buchstaben vertikal
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            letters.forEach { letter ->
                Text(
                    text = letter.toString(),
                    style = TextStyle(
                        fontSize = 3.em,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center,
                        color = textColor,
                        fontStyle = FontStyle.Normal,
                        shadow = shadowStyle
                    ),
                    modifier = Modifier
//                        .wrapContentHeight()
                        .height(17.dp)
//                        .fillMaxHeight()
                        .fillMaxWidth()
                        .clickable(onClick = { onLetterClicked(letter) })
                )
            }
        }
    }
}

private fun getLetterAtPosition(y: Float, totalHeight: Float, letters: List<Char>): Char {
    val totalLetters = letters.size
    val letterHeight = totalHeight / totalLetters
    val index = (y / letterHeight).toInt().coerceIn(0, totalLetters - 1)
    return letters[index]
}
