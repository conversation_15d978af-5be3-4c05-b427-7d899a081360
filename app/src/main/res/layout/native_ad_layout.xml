<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="0dp">

    <!-- Hauptcontainer -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <!-- Ad-Bild -->
        <ImageView
            android:id="@+id/ad_image"
            android:layout_width="80dp"
            android:layout_height="50dp"
            android:layout_marginEnd="8dp"
            android:layout_margin="4dp"
            android:contentDescription="Ad Image"
            android:scaleType="centerCrop"
            android:background="@android:color/transparent" />

        <!-- Textinformationen -->
        <LinearLayout
            android:layout_width="0dp"
            android:padding="4dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/ad_headline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textColor="@android:color/white"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/ad_body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:maxLines="2" />
        </LinearLayout>

        <!-- Call-to-Action Button -->
        <Button
            android:id="@+id/ad_call_to_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="11sp"
            android:padding="2dp"
            android:layout_marginTop="10dp"
            android:contentDescription="@string/ad_call_to_action_content_description"
            android:backgroundTint="@android:color/black"
            android:textColor="@android:color/white" />
    </LinearLayout>

    <!-- "Anzeige"-Label oben rechts -->
    <TextView
        android:id="@+id/ad_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|start"
        android:background="#FBBC04"
        android:paddingHorizontal="2dp"
        android:text="@string/ad_text"
        android:textColor="@android:color/black"
        android:textSize="12sp" />
</com.google.android.gms.ads.nativead.NativeAdView>
