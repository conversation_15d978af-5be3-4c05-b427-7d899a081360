<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Theme.DetoxLauncher" parent="android:Theme.Material.Light.NoActionBar">
        <!-- Transparente Statusleiste -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>

        <!-- IMPORTANT for Statusbar Camera cutout in combination with
        .padding(WindowInsets.systemBarsIgnoringVisibility.asPaddingValues())
        This combination helps to show statusbar transparent and let's content scroll OVER statusbar -->
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>

        <!-- SHOW WALLPAPER -->
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>

    </style>

</resources>